#include "stuck_detection_recovery.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>
#include <iomanip>
#include <sstream>

namespace fescue_iox
{

StuckDetectionRecovery::StuckDetectionRecovery(const StuckRecoveryParam &param)
    : param_(param)
    , current_linear_speed_(param.initial_linear_speed)
    , current_angular_speed_(param.initial_angular_speed)
    , recovery_start_time_(0)
    , last_action_time_(0)
    , recovery_cycle_count_(0)
{
    LOG_INFO("[StuckDetectionRecovery] 初始化脱困恢复系统");
}

StuckDetectionRecovery::~StuckDetectionRecovery()
{
    Shutdown();
    LOG_INFO("[StuckDetectionRecovery] 脱困恢复系统已关闭");
}

void StuckDetectionRecovery::Initialize()
{
    LOG_INFO("[StuckDetectionRecovery] 初始化脱困检测和恢复系统");

    // 初始化数据记录
    if (param_.enable_data_logging)
    {
        InitializeDataLogging();
    }

    // 启动检测线程（但不激活检测）
    detection_running_.store(true);
    detection_thread_ = std::thread(&StuckDetectionRecovery::DetectionThread, this);

    // 启动恢复线程
    recovery_running_.store(true);
    recovery_thread_ = std::thread(&StuckDetectionRecovery::RecoveryThread, this);

    LOG_INFO("[StuckDetectionRecovery] 脱困检测和恢复线程已启动，等待激活");
}

void StuckDetectionRecovery::Shutdown()
{
    LOG_INFO("[StuckDetectionRecovery] 关闭脱困检测和恢复线程");

    // 停止线程
    detection_running_.store(false);
    recovery_running_.store(false);

    if (detection_thread_.joinable())
    {
        detection_thread_.join();
    }

    if (recovery_thread_.joinable())
    {
        recovery_thread_.join();
    }

    // 关闭数据记录
    CloseDataLogging();
}

void StuckDetectionRecovery::StartDetection()
{
    bool was_active = detection_active_.exchange(true);
    if (!was_active)
    {
        LOG_INFO("[StuckDetectionRecovery] 激活脱困检测");
        // 清理历史数据，重新开始检测
        std::lock_guard<std::mutex> lock(movement_mutex_);
        movement_history_.clear();
        is_stuck_.store(false);
    }
}

void StuckDetectionRecovery::StopDetection()
{
    bool was_active = detection_active_.exchange(false);
    if (was_active)
    {
        LOG_INFO("[StuckDetectionRecovery] 暂停脱困检测");
        // 停止任何正在进行的恢复
        StopRecovery();
        is_stuck_.store(false);
    }
}

bool StuckDetectionRecovery::IsDetectionActive() const
{
    return detection_active_.load();
}

void StuckDetectionRecovery::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_mutex_);
    latest_imu_data_ = imu_data;
    imu_data_valid_ = true;

    // 处理IMU数据
    ProcessImuData(imu_data);
}

void StuckDetectionRecovery::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_mutex_);
    latest_motor_data_ = motor_speed_data;
    motor_data_valid_ = true;

    // 处理电机数据
    ProcessMotorData(motor_speed_data);
}

bool StuckDetectionRecovery::IsStuck()
{
    return is_stuck_.load();
}

bool StuckDetectionRecovery::StartRecovery()
{
    if (recovery_active_.load())
    {
        LOG_WARN("[StuckDetectionRecovery] 恢复已在进行中");
        return false;
    }

    LOG_INFO("[StuckDetectionRecovery] 开始脱困恢复");
    recovery_active_.store(true);
    recovery_start_time_ = GetCurrentTimestamp();
    last_action_time_ = recovery_start_time_;
    recovery_cycle_count_ = 0;

    // 重置速度为初始值
    current_linear_speed_ = param_.initial_linear_speed;
    current_angular_speed_ = param_.initial_angular_speed;

    // 记录恢复开始位置
    ResetMovementTracking();

    return true;
}

void StuckDetectionRecovery::StopRecovery()
{
    if (!recovery_active_.load())
    {
        return;
    }

    LOG_INFO("[StuckDetectionRecovery] 停止脱困恢复");
    recovery_active_.store(false);
    current_recovery_mode_ = RecoveryMode::NONE;

    // 停止运动
    PublishVelocity(0.0f, 0.0f, 100);
}

bool StuckDetectionRecovery::IsRecoveryActive() const
{
    return recovery_active_.load();
}

void StuckDetectionRecovery::ResetAllStates()
{
    LOG_INFO("[StuckDetectionRecovery] 重置所有状态和变量");

    // 重置检测状态
    is_stuck_.store(false);
    detection_results_.clear();

    // 重置恢复状态
    recovery_active_.store(false);
    current_recovery_mode_ = RecoveryMode::NONE;
    current_linear_speed_ = param_.initial_linear_speed;
    current_angular_speed_ = param_.initial_angular_speed;
    recovery_start_time_ = 0;
    last_action_time_ = 0;
    recovery_cycle_count_ = 0;

    // 重置运动数据
    {
        std::lock_guard<std::mutex> lock(movement_mutex_);
        movement_history_.clear();
        current_movement_ = MovementData();
        recovery_start_position_ = MovementData();
    }

    // 重置IMU相关状态
    is_first_imu_ = true;
    is_bias_calibrated_ = false;
    bias_z_ = 0.0f;
    calibration_samples_.clear();
    filter_initialized_ = false;
    filtered_angular_velocity_ = 0.0f;

    LOG_INFO("[StuckDetectionRecovery] 所有状态和变量已重置");
}

bool StuckDetectionRecovery::IsInRecoverySuccessCooldown() const
{
    if (recovery_success_time_ == 0)
    {
        return false; // 从未有过恢复成功
    }

    uint64_t current_time = GetCurrentTimestamp();
    uint64_t elapsed_time = current_time - recovery_success_time_;

    return elapsed_time < RECOVERY_SUCCESS_COOLDOWN_MS;
}

void StuckDetectionRecovery::SetVelocityPublisher(std::shared_ptr<VelocityPublisher> vel_publisher)
{
    vel_publisher_ = vel_publisher;
}

void StuckDetectionRecovery::SetParam(const StuckRecoveryParam &param)
{
    param_ = param;
}

StuckRecoveryParam StuckDetectionRecovery::GetParam() const
{
    return param_;
}

RecoveryMode StuckDetectionRecovery::GetCurrentRecoveryMode() const
{
    return current_recovery_mode_;
}

std::vector<WindowDetectionResult> StuckDetectionRecovery::GetDetectionResults() const
{
    return detection_results_;
}

void StuckDetectionRecovery::DetectionThread()
{
    LOG_INFO("[StuckDetectionRecovery] 检测线程已启动");

    while (detection_running_.load())
    {
        // 只有在检测激活时才进行检测
        if (detection_active_.load())
        {
            // 更新运动数据
            UpdateMovementData();

            // 检查是否在恢复成功冷却期内
            if (IsInRecoverySuccessCooldown())
            {
                // 在冷却期内，不进行脱困检测，确保被困状态为false
                is_stuck_.store(false);
                LOG_INFO_THROTTLE(10000, "[StuckDetectionRecovery] 恢复成功冷却期内，暂停脱困检测");
            }
            else
            {
                // 执行多窗口检测
                bool stuck_detected = IsStuckInMultipleWindows();

                // 更新被困状态
                bool previous_stuck = is_stuck_.exchange(stuck_detected);

                if (stuck_detected && !previous_stuck)
                {
                    LOG_WARN("[StuckDetectionRecovery] 检测到被困状态");
                }
                else if (!stuck_detected && previous_stuck)
                {
                    LOG_INFO("[StuckDetectionRecovery] 被困状态已解除");
                }
            }
        }
        else
        {
            // 检测未激活时，确保被困状态为false
            is_stuck_.store(false);
        }

        // 控制检测频率 (10Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG_INFO("[StuckDetectionRecovery] 检测线程已退出");
}

void StuckDetectionRecovery::RecoveryThread()
{
    LOG_INFO("[StuckDetectionRecovery] 恢复线程已启动");

    while (recovery_running_.load())
    {
        if (recovery_active_.load())
        {
            uint64_t current_time = GetCurrentTimestamp();

            // 检查是否超过最大恢复时间
            if (current_time - recovery_start_time_ > param_.max_recovery_duration_ms)
            {
                LOG_WARN("[StuckDetectionRecovery] 恢复超时，停止恢复");
                StopRecovery();

                // TODO 加异常
                continue;
            }

            // 检查恢复期间是否有运动
            if (HasMovementDuringRecovery())
            {
                LOG_INFO("[StuckDetectionRecovery] 检测到运动，恢复成功");

                // 记录恢复成功时间，启动1分钟冷却期
                recovery_success_time_ = GetCurrentTimestamp();
                LOG_INFO("[StuckDetectionRecovery] 恢复成功，启动1分钟冷却期，期间不会进行脱困检测");

                // 停止恢复
                StopRecovery();

                // 重置所有状态和变量
                ResetAllStates();

                continue;
            }

            // 执行恢复动作
            if (current_time - last_action_time_ > param_.recovery_action_duration_ms)
            {
                // 切换到下一个恢复模式
                current_recovery_mode_ = GetNextRecoveryMode();

                // 渐进式调整速度
                ProgressiveSpeedAdjustment();

                // 执行恢复动作
                ExecuteRecoveryAction(current_recovery_mode_, current_linear_speed_, current_angular_speed_);

                last_action_time_ = current_time;
                recovery_cycle_count_++;

                LOG_INFO("[StuckDetectionRecovery] 执行恢复动作: 模式={}, 线速度={:.2f}, 角速度={:.2f}",
                         static_cast<int>(current_recovery_mode_), current_linear_speed_, current_angular_speed_);
            }
            else
            {
                // 执行恢复动作
                ExecuteRecoveryAction(current_recovery_mode_, current_linear_speed_, current_angular_speed_);
            }
        }

        // 控制恢复频率 (20Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    LOG_INFO("[StuckDetectionRecovery] 恢复线程已退出");
}

void StuckDetectionRecovery::ProcessImuData(const ImuData &imu_data)
{
    // 仿照nav_cross_region的IMU处理方式
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;

    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false;
        LOG_INFO("[StuckDetectionRecovery] 忽略第一次IMU数据");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        return;
    }

    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // IMU零偏校准
    if (!is_bias_calibrated_)
    {
        // 忽略第一秒数据进行校准
        if (dt > 0.0f && calibration_samples_.size() < CALIBRATION_SAMPLES)
        {
            calibration_samples_.push_back(imu_data.angular_velocity_z);
        }

        if (calibration_samples_.size() >= CALIBRATION_SAMPLES)
        {
            // 计算角速度零偏
            float sum_angular = 0.0f;
            for (float sample : calibration_samples_)
            {
                sum_angular += sample;
            }
            bias_z_ = sum_angular / calibration_samples_.size();

            is_bias_calibrated_ = true;

            LOG_INFO("[StuckDetectionRecovery] IMU零偏校准完成: bias_z = {:.4f}", bias_z_);
        }
        return;
    }

    // 应用零偏校正 - 角速度
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;

    // 保存原始数据用于记录
    float raw_angular_velocity = angular_velocity_z;

    // 初始化滤波器（仅在第一次使用时）
    if (!filter_initialized_)
    {
        InitializeFilters(angular_velocity_z);
        filter_initialized_ = true;
    }

    // 应用低通滤波
    angular_velocity_z = ApplyLowPassFilter(angular_velocity_z, filtered_angular_velocity_, filter_alpha_);

    // 记录滤波前后的数据
    if (param_.enable_data_logging)
    {
        LogFilteringData(imu_data.system_timestamp, raw_angular_velocity, angular_velocity_z);
    }

    // 应用阈值滤波 - 角速度
    if (std::abs(angular_velocity_z) < angular_velocity_z_threshold_)
    {
        angular_velocity_z = 0.0f;
    }

    // 更新当前运动数据
    std::lock_guard<std::mutex> lock(movement_mutex_);
    current_movement_.angular_velocity = angular_velocity_z;
    current_movement_.angular_displacement += std::abs(angular_velocity_z) * dt;
    current_movement_.timestamp = imu_data.system_timestamp;
}

void StuckDetectionRecovery::ProcessMotorData(const MotorSpeedData &motor_data)
{
    // 简化处理，仅记录接收到电机数据
    LOG_INFO_THROTTLE(5000, "[StuckDetectionRecovery] 接收到电机数据: 左轮={:.1f}RPM, 右轮={:.1f}RPM",
                      motor_data.motor_speed_left, motor_data.motor_speed_right);
}

void StuckDetectionRecovery::UpdateMovementData()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);

    uint64_t current_time = GetCurrentTimestamp();

    // 如果有新的运动数据，添加到历史记录
    if (current_movement_.timestamp > 0)
    {
        MovementData data = current_movement_;
        data.timestamp = current_time;

        movement_history_.push_back(data);

        // 记录数据
        if (param_.enable_data_logging)
        {
            LogData(data);
        }

        // 重置当前运动数据
        current_movement_ = MovementData();
    }

    // 使用通用滑动窗口清理函数，保留最长窗口时间的数据（15分钟 + 1分钟缓冲）
    CleanupMovementHistory(16 * 60 * 1000); // 16分钟
}

WindowDetectionResult StuckDetectionRecovery::CheckWindow(uint64_t window_duration_ms, float min_rotation)
{
    WindowDetectionResult result;
    result.window_duration_ms = window_duration_ms;

    uint64_t current_time = GetCurrentTimestamp();

    // 使用通用滑动窗口函数获取窗口数据
    std::deque<MovementData> window_data = GetWindowData(window_duration_ms, current_time);

    // 检查数据有效性：需要有足够的历史数据才能进行检测
    if (window_data.empty())
    {
        LOG_INFO("[StuckDetectionRecovery] 窗口{}ms运动数据为空，跳过检测", window_duration_ms);
        result.is_stuck = false; // 数据不足时不判断为被困
        result.data_insufficient = true;
        return result;
    }

    // 检查数据覆盖时间是否足够
    uint64_t earliest_timestamp = window_data.front().timestamp;
    uint64_t latest_timestamp = window_data.back().timestamp;
    uint64_t data_coverage_duration = latest_timestamp - earliest_timestamp;

    // 如果数据覆盖时间不足窗口时间的80%，则认为数据不足
    uint64_t min_coverage_duration = window_duration_ms * 0.8;
    if (data_coverage_duration < min_coverage_duration)
    {
        LOG_INFO("[StuckDetectionRecovery] 窗口{}ms数据覆盖不足({:.1f}s < {:.1f}s)，跳过检测",
                 window_duration_ms, data_coverage_duration / 1000.0f, min_coverage_duration / 1000.0f);
        result.is_stuck = false; // 数据覆盖不足时不判断为被困
        result.data_insufficient = true;
        return result;
    }

    // 计算窗口内的总旋转
    int data_points_in_window = window_data.size();
    for (const auto &data : window_data)
    {
        result.total_rotation += std::abs(data.angular_displacement);
    }

    // 确保窗口内有足够的数据点（至少10个数据点，对应1秒的数据）
    if (data_points_in_window < 10)
    {
        LOG_INFO("[StuckDetectionRecovery] 窗口{}ms内数据点不足({} < 10)，跳过检测",
                 window_duration_ms, data_points_in_window);
        result.is_stuck = false; // 数据点不足时不判断为被困
        result.data_insufficient = true;
        return result;
    }

    // 判断是否被困 - 仅基于角度累计
    result.is_stuck = (result.total_rotation < min_rotation);
    result.data_insufficient = false;

    return result;
}

bool StuckDetectionRecovery::IsStuckInMultipleWindows()
{
    // 使用5分钟、10分钟、15分钟的滑动窗口检测
    std::vector<WindowDetectionResult> window_results;
    detection_results_.clear();

    // 定义窗口时间和对应的阈值
    struct WindowConfig
    {
        int minutes;
        float threshold;
    };

    std::vector<WindowConfig> windows = {
        {5, param_.rotation_threshold_5min},
        {10, param_.rotation_threshold_10min},
        {15, param_.rotation_threshold_15min}};

    int valid_windows = 0;
    int stuck_windows = 0;

    // 检查每个窗口
    for (const auto &window : windows)
    {
        uint64_t window_duration_ms = window.minutes * 60 * 1000;
        WindowDetectionResult result = CheckWindow(window_duration_ms, window.threshold);

        window_results.push_back(result);
        detection_results_.push_back(result);

        // 只统计有效窗口
        if (!result.data_insufficient)
        {
            valid_windows++;
            if (result.is_stuck)
            {
                stuck_windows++;
            }
        }
    }

    // 检查数据是否充足：至少需要5分钟窗口有足够数据
    if (valid_windows == 0)
    {
        LOG_INFO("[StuckDetectionRecovery] 所有窗口数据不足，跳过脱困检测");
        return false;
    }

    // 当至少有指定数量的窗口检测到被困时，判断为受困
    bool is_stuck = stuck_windows >= param_.min_stuck_windows;

    if (is_stuck)
    {
        LOG_WARN("[StuckDetectionRecovery] 检测被困（is_stuck为1）: 有效窗口={}, 被困窗口={}, 最小被困窗口数={}",
                 valid_windows, stuck_windows, param_.min_stuck_windows);

        // 详细记录每个窗口的情况
        for (size_t i = 0; i < window_results.size(); i++)
        {
            const auto &result = window_results[i];
            const auto &window = windows[i];
            LOG_WARN("[StuckDetectionRecovery] {}分钟窗口: 角度累计={:.3f}rad, 阈值={:.3f}rad, 被困={}, 数据={}",
                     window.minutes, result.total_rotation, window.threshold,
                     result.is_stuck ? "是" : "否", result.data_insufficient ? "不足" : "充足");
        }
    }
    else
    {
        LOG_INFO("[StuckDetectionRecovery] 检测正常（is_stuck为0）: 有效窗口={}, 被困窗口={}, 最小被困窗口数={}",
                 valid_windows, stuck_windows, param_.min_stuck_windows);

        // 详细记录每个窗口的情况
        for (size_t i = 0; i < window_results.size(); i++)
        {
            const auto &result = window_results[i];
            const auto &window = windows[i];
            LOG_WARN("[StuckDetectionRecovery] {}分钟窗口: 角度累计={:.3f}rad, 阈值={:.3f}rad, 被困={}, 数据={}",
                     window.minutes, result.total_rotation, window.threshold,
                     result.is_stuck ? "是" : "否", result.data_insufficient ? "不足" : "充足");
        }
    }

    return is_stuck;
}

void StuckDetectionRecovery::ExecuteRecoveryAction(RecoveryMode mode, float linear_speed, float angular_speed)
{
    switch (mode)
    {
    case RecoveryMode::ROTATE_LEFT:
        PublishVelocity(0.0f, angular_speed);
        break;

    case RecoveryMode::ROTATE_RIGHT:
        PublishVelocity(0.0f, -angular_speed);
        break;

    case RecoveryMode::FORWARD:
        PublishVelocity(linear_speed, 0.0f);
        break;

    case RecoveryMode::BACKWARD:
        PublishVelocity(-linear_speed, 0.0f);
        break;

    case RecoveryMode::SINGLE_WHEEL_LEFT:
    {
        // 左轮高速旋转，右轮慢速或静止
        float left_vel = 1.0;
        float right_vel = 0.0;
        float linear_speed = (left_vel + right_vel) / 2.0f;
        float angular_speed = (right_vel - left_vel) / param_.wheel_base;
        PublishVelocity(linear_speed, angular_speed);
        break;
    }

    case RecoveryMode::SINGLE_WHEEL_RIGHT:
    {
        // 右轮高速旋转，左轮慢速或静止
        float left_vel = 0.0;
        float right_vel = 1.0;
        float linear_speed = (left_vel + right_vel) / 2.0f;
        float angular_speed = (right_vel - left_vel) / param_.wheel_base;
        PublishVelocity(linear_speed, angular_speed);
        break;
    }

    case RecoveryMode::ALTERNATING_PUSH:
        // 交替前进后退
        if (recovery_cycle_count_ % 2 == 0)
        {
            PublishVelocity(linear_speed, 0.0f);
        }
        else
        {
            PublishVelocity(-linear_speed, 0.0f);
        }
        break;

    default:
        PublishVelocity(0.0f, 0.0f);
        break;
    }
}

RecoveryMode StuckDetectionRecovery::GetNextRecoveryMode()
{
    // 循环使用不同的恢复模式
    static const std::vector<RecoveryMode> recovery_modes = {
        RecoveryMode::ROTATE_LEFT,
        RecoveryMode::ROTATE_RIGHT,
        RecoveryMode::BACKWARD,
        RecoveryMode::FORWARD,
        RecoveryMode::SINGLE_WHEEL_LEFT,
        RecoveryMode::SINGLE_WHEEL_RIGHT,
        RecoveryMode::ALTERNATING_PUSH};

    int mode_index = recovery_cycle_count_ % recovery_modes.size();
    return recovery_modes[mode_index];
}

void StuckDetectionRecovery::ProgressiveSpeedAdjustment()
{
    // 每几个周期增加速度
    if (recovery_cycle_count_ > 0 && recovery_cycle_count_ % 3 == 0)
    {
        current_linear_speed_ = std::min(current_linear_speed_ + param_.speed_increment,
                                         param_.max_linear_speed);
        current_angular_speed_ = std::min(current_angular_speed_ + param_.speed_increment,
                                          param_.max_angular_speed);

        LOG_INFO("[StuckDetectionRecovery] 速度递增: 线速度={:.2f}, 角速度={:.2f}",
                 current_linear_speed_, current_angular_speed_);
    }
}

bool StuckDetectionRecovery::HasMovementDuringRecovery()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);

    // 计算恢复开始以来的总角度
    float total_rotation = 0.0f;

    uint64_t recovery_start = recovery_start_time_;

    for (const auto &data : movement_history_)
    {
        if (data.timestamp >= recovery_start)
        {
            total_rotation += std::abs(data.angular_displacement);
        }
    }

    return total_rotation > recovery_rotation_threshold_;
}

void StuckDetectionRecovery::ResetMovementTracking()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);
    recovery_start_position_ = MovementData();
    recovery_start_position_.timestamp = GetCurrentTimestamp();
}

void StuckDetectionRecovery::InitializeDataLogging()
{
    try
    {
        data_log_file_.open(param_.log_file_path, std::ios::out | std::ios::trunc);
        if (data_log_file_.is_open())
        {
            // 写入CSV头部
            data_log_file_ << "timestamp,angular_displacement,angular_velocity\n";
            data_logging_initialized_ = true;
            LOG_INFO("[StuckDetectionRecovery] 数据记录已初始化: {}", param_.log_file_path);
        }
        else
        {
            LOG_ERROR("[StuckDetectionRecovery] 无法打开数据记录文件: {}", param_.log_file_path);
        }

        // 初始化滤波数据记录文件
        std::string filter_log_path = "/userdata/log/stuck_recovery_filter_data.csv";
        filter_log_file_.open(filter_log_path, std::ios::out | std::ios::trunc);
        if (filter_log_file_.is_open())
        {
            // 写入CSV头部
            filter_log_file_ << "timestamp,raw_angular_velocity,filtered_angular_velocity\n";
            LOG_INFO("[StuckDetectionRecovery] 滤波数据记录已初始化: {}", filter_log_path);
        }
        else
        {
            LOG_ERROR("[StuckDetectionRecovery] 无法打开滤波数据记录文件: {}", filter_log_path);
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[StuckDetectionRecovery] 数据记录初始化失败: {}", e.what());
    }
}

void StuckDetectionRecovery::LogData(const MovementData &data)
{
    if (data_logging_initialized_ && data_log_file_.is_open())
    {
        data_log_file_ << data.timestamp << ","
                       << data.angular_displacement << ","
                       << data.angular_velocity << "\n";
        data_log_file_.flush();
    }
}

void StuckDetectionRecovery::LogFilteringData(uint64_t timestamp, float raw_angular_vel, float filtered_angular_vel)
{
    if (data_logging_initialized_ && filter_log_file_.is_open())
    {
        filter_log_file_ << timestamp << ","
                         << raw_angular_vel << ","
                         << filtered_angular_vel << "\n";
        filter_log_file_.flush();
    }
}

void StuckDetectionRecovery::CloseDataLogging()
{
    if (data_log_file_.is_open())
    {
        data_log_file_.close();
        LOG_INFO("[StuckDetectionRecovery] 数据记录已关闭");
    }
    if (filter_log_file_.is_open())
    {
        filter_log_file_.close();
        LOG_INFO("[StuckDetectionRecovery] 滤波数据记录已关闭");
    }
    data_logging_initialized_ = false;
}

uint64_t StuckDetectionRecovery::GetCurrentTimestamp() const
{
    return GetTimestampMs();
}

float StuckDetectionRecovery::CalculateRotation(float angular_vel, float dt)
{
    return std::abs(angular_vel) * dt;
}

void StuckDetectionRecovery::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
    }
}

float StuckDetectionRecovery::ApplyLowPassFilter(float new_value, float &filtered_value, float alpha)
{
    // 一阶低通滤波器: filtered_value = alpha * new_value + (1 - alpha) * filtered_value
    // alpha越小，滤波越强（更平滑但响应更慢）
    filtered_value = alpha * new_value + (1.0f - alpha) * filtered_value;
    return filtered_value;
}

void StuckDetectionRecovery::InitializeFilters(float initial_angular_velocity)
{
    filtered_angular_velocity_ = initial_angular_velocity;
    LOG_INFO("[StuckDetectionRecovery] 滤波器已初始化: 角速度={:.4f}", initial_angular_velocity);
}

void StuckDetectionRecovery::CleanupMovementHistory(uint64_t max_retention_time_ms)
{
    // 注意：此函数假设调用者已经持有 movement_mutex_ 锁
    uint64_t current_time = GetCurrentTimestamp();
    uint64_t cutoff_time = current_time - max_retention_time_ms;

    // 清理超过保留时间的历史数据
    while (!movement_history_.empty() && movement_history_.front().timestamp < cutoff_time)
    {
        movement_history_.pop_front();
    }
}

std::deque<MovementData> StuckDetectionRecovery::GetWindowData(uint64_t window_duration_ms, uint64_t current_time)
{
    std::deque<MovementData> window_data;
    uint64_t window_start_time = current_time - window_duration_ms;

    std::lock_guard<std::mutex> lock(movement_mutex_);

    // 从历史数据中提取指定时间窗口的数据
    for (const auto &data : movement_history_)
    {
        if (data.timestamp >= window_start_time && data.timestamp <= current_time)
        {
            window_data.push_back(data);
        }
    }

    return window_data;
}

} // namespace fescue_iox
